import { useState, useEffect } from "react";
import { Box, Button, Grid, Tab, Tabs } from "@mui/material";
import { useAddNewOffer } from "./useAddNewOffer";
import { IPropsAddNewOffer, ITabPanelProps } from "./IAddNewOffer";
import ShoppingCartOffer from "../ShoppingCartOffer/ShoppingCartOffer";
import OfferAddons from "./OfferAddons";
import { useTranslation } from "react-i18next";
import { OrderConfirmationModal } from "@common/styleComponents/OrderConfirmationModal/OrderConfirmationModal";
import ScheduleInstallation from "../../Views/ScheduleInstallation/ScheduleInstallation";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
// import { isEmpty } from "lodash";
import { useSnackBar } from "@common";
import { ICollectionActiveServices } from "@services/subscription/accountId/interface/IActiveServices";

function CustomTabPanel(props: ITabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            aria-labelledby={`simple-tab-${index}`}
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            role="tabpanel"
            {...other}
        >
            {value === index && <Box sx={{ pt: 1 }}>{children}</Box>}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
    };
}

const AddNewOffer = ({
    subscriptions,
    onClickCloseOffer,
    setSuccessScheduleInstallation,
    setScheduleInstallation,
    contactDetails,
    setCorrelationId,
    correlationId,
    scheduleSuccess,
    setScheduleSuccess,
    setSuccessRisk,
    setAddNewAddOns,
}: IPropsAddNewOffer) => {
    const { t } = useTranslation(["customer"]);
    const { setSnackBarSuccess } = useSnackBar();

    const {
        handleChange,
        value,
        offers,
        subscriptionsActives,
        totalAddons,
        handleAddClick,
        handleRemoveClick,
        addedAddons,
        callWfeFacade,
        modalInfo,
        handleRedirectAddAddon,
    } = useAddNewOffer({
        subscriptions,
        setSuccessRisk,
        setAddNewAddOns,
    });

    const [showSchedule, setShowSchedule] = useState(false);
    const [hasScheduleAddon, setHasScheduleAddon] = useState(false);

    useEffect(() => {
        
        console.log(" ### AddNewOffer ### "+addedAddons);

        const selected = addedAddons.some((addon) => {
            console.log('Revisando addon:', addon);
            return addon.offer.code === "MATERIAL";
        });

        setHasScheduleAddon(selected);
    }, [addedAddons]);

    const handleConfirm = (scheduleData?: ISlot) => {
        if (hasScheduleAddon) {

            console.log(" Hay que agendar visita tecnica");

            const filterSubscriptionsTest = subscriptions?.collection;
            callWfeFacade({
                scheduleParams: scheduleData,
                contactDetails: contactDetails,
                correlationId: correlationId ?? "",
                subscriptionsList: filterSubscriptionsTest ?? [],
                // debemos agregar un parametro extra para notificarle al wfe que se debe programar la instalacion
                appointmentConfirmation: true,
            });
            setShowSchedule(true);

        } else {
            const filterSubscriptionsTest = subscriptions?.collection;
            // eslint-disable-next-line no-console
            console.log(filterSubscriptionsTest);

            // Llamar a callWfeFacade con el array completo de suscripciones
            callWfeFacade({
                scheduleParams: scheduleData,
                contactDetails: contactDetails,
                correlationId: correlationId ?? "",
                subscriptionsList: filterSubscriptionsTest ?? [],
            });
        }
    };

    const activeAddonsObj: Record<
        string,
        {
            activeQuantities: number;
            addonCode: string;
            maxQuantity: number;
            subscriptions: ICollectionActiveServices[];
        }
    > = {};

    offers.forEach((offer) => {
        offer.addons.forEach((addon) => {
            const filterByAddons = subscriptionsActives.filter(({ catalogCode }) => addon.code === catalogCode);

            if (!filterByAddons.length) {
                return;
            }

            if (!activeAddonsObj[addon.code]) {
                activeAddonsObj[addon.code] = {
                    activeQuantities: 0,
                    addonCode: addon.code,
                    maxQuantity: addon.maxQuantity,
                    subscriptions: [],
                };
            }

            activeAddonsObj[addon.code].activeQuantities += filterByAddons.length;
            activeAddonsObj[addon.code].subscriptions.push(...filterByAddons);
        });
    });

    useEffect(() => {
        if (scheduleSuccess) {
            setSnackBarSuccess(t("customer:scheduleSuccess"));
        }
    }, [scheduleSuccess, setSnackBarSuccess, t]);

    return (
        <div>
            {modalInfo && (
                <OrderConfirmationModal
                    isOpen
                    orderReference={modalInfo.orderReferences?.[0] ?? ""}
                    orderReferences={modalInfo.orderReferences}
                    onClose={handleRedirectAddAddon}
                />
            )}
            {showSchedule ? (
                <ScheduleInstallation
                    contact={contactDetails}
                    scheduleSuccess={scheduleSuccess}
                    setCorrelationId={setCorrelationId}
                    setScheduleInstallation={setScheduleInstallation}
                    setSuccessScheduleInstallation={setSuccessScheduleInstallation}
                />
            ) : (
                <Grid container direction="row" spacing={3}>
                    <Grid item md={6} xs={12}>
                        <Tabs aria-label="add new offers" value={value} variant="fullWidth" onChange={handleChange}>
                            <Tab label="TV" {...a11yProps(0)} />
                            <Tab label="INTERNET" {...a11yProps(1)} />
                            <Tab label="MATERIAL" {...a11yProps(2)} />
                        </Tabs>
                        <CustomTabPanel index={0} value={value}>
                            <Grid container direction="row" spacing={2}>
                                {offers
                                    .filter((offer) => offer.code === "TV_CHANNEL")
                                    .map((offer) =>
                                        offer?.addons?.map((addon) => {
                                            const addedAddon = addedAddons.find(
                                                (item) => item.addon.code === addon.code
                                            );
                                            const currentActiveQty = activeAddonsObj[addon.code]?.activeQuantities ?? 0;
                                            const quantity = addedAddon ? addedAddon.quantity : 0;
                                            const isDisabled = currentActiveQty >= addon.maxQuantity;

                                            return (
                                                <OfferAddons
                                                    addon={addon}
                                                    disabled={isDisabled}
                                                    handleAddClick={() =>
                                                        handleAddClick(addon, offer, currentActiveQty)
                                                    }
                                                    handleRemoveClick={() => handleRemoveClick(addon, offer)}
                                                    isAdded={quantity > 0}
                                                    key={addon.code}
                                                    quantity={quantity}
                                                />
                                            );
                                        })
                                    )}
                            </Grid>
                        </CustomTabPanel>
                        <CustomTabPanel index={1} value={value}>
                            <Grid container direction="row" spacing={2}>
                                {offers
                                    .filter((offer) => (offer.code !== "TV_CHANNEL" && offer.code !== "MATERIAL"))
                                    .map((offer) =>
                                        offer?.addons?.map((addon) => {
                                            const addedAddon = addedAddons.find(
                                                (item) => item.addon.code === addon.code
                                            );
                                            const currentActiveQty = activeAddonsObj[addon.code]?.activeQuantities ?? 0;
                                            const quantity = addedAddon ? addedAddon.quantity : 0;
                                            const isDisabled = currentActiveQty >= addon.maxQuantity;

                                            return (
                                                <OfferAddons
                                                    addon={addon}
                                                    disabled={isDisabled}
                                                    handleAddClick={() =>
                                                        handleAddClick(addon, offer, currentActiveQty)
                                                    }
                                                    handleRemoveClick={() => handleRemoveClick(addon, offer)}
                                                    isAdded={quantity > 0}
                                                    key={addon.code}
                                                    quantity={quantity}
                                                />
                                            );
                                        })
                                    )}
                            </Grid>
                        </CustomTabPanel>
                        <CustomTabPanel index={2} value={value}>
                            <Grid container direction="row" spacing={2}>
                                {offers
                                    .filter((offer) => offer.code === "MATERIAL")
                                    .map((offer) =>
                                        offer?.addons?.map((addon) => {
                                            const addedAddon = addedAddons.find(
                                                (item) => item.addon.code === addon.code
                                            );
                                            const currentActiveQty = activeAddonsObj[addon.code]?.activeQuantities ?? 0;
                                            const quantity = addedAddon ? addedAddon.quantity : 0;
                                            const isDisabled = currentActiveQty >= addon.maxQuantity;

                                            return (
                                                <OfferAddons
                                                    addon={addon}
                                                    disabled={isDisabled}
                                                    handleAddClick={() =>
                                                        handleAddClick(addon, offer, currentActiveQty)
                                                    }
                                                    handleRemoveClick={() => handleRemoveClick(addon, offer)}
                                                    isAdded={quantity > 0}
                                                    key={addon.code}
                                                    quantity={quantity}
                                                />
                                            );
                                        })
                                    )}
                            </Grid>
                        </CustomTabPanel>
                    </Grid>
                    <Grid item md={6} xs={12}>
                        <ShoppingCartOffer addedAddons={addedAddons} totalAddons={totalAddons} />
                        <Box sx={{ py: 1, px: { xs: 2, sm: 4 } }}>
                            <Grid container spacing={2}>
                                <Grid item xs={6}>
                                    <Button
                                        color="inherit"
                                        fullWidth
                                        variant="contained"
                                        onClick={() => {
                                            onClickCloseOffer?.();
                                            setAddNewAddOns(false);
                                            setSuccessRisk(null);
                                        }}
                                    >
                                        {t("customer:cancelSubscriptionAddOns")}
                                    </Button>
                                </Grid>
                                <Grid item xs={6}>
                                    <Button
                                        disabled={!addedAddons.length}
                                        fullWidth
                                        sx={{ mb: 2 }}
                                        variant="contained"
                                        onClick={() => handleConfirm()}
                                    >
                                        {hasScheduleAddon ? t("customer:continue") : t("customer:confirm")}
                                    </Button>
                                </Grid>
                            </Grid>
                        </Box>
                    </Grid>
                </Grid>
            )}
            {showSchedule && (
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <Button
                            color="inherit"
                            fullWidth
                            sx={{ mt: 2 }}
                            variant="contained"
                            onClick={() => {
                                onClickCloseOffer?.();
                                setScheduleSuccess(false);
                                setSuccessRisk(null);
                                setAddNewAddOns(false);
                                setScheduleInstallation({
                                    finish: "",
                                    start: "",
                                    grade: 0,
                                });
                            }}
                        >
                            {t("customer:cancelSubscriptionAddOns")}
                        </Button>
                    </Grid>
                    {/*
        <Grid item xs={6}>
            <Button
                color={scheduleSuccess ? "primary" : "secondary"}
                disabled={isEmpty(props.scheduleInstallation?.finish)}
                fullWidth
                sx={{ mt: 2 }}
                variant="contained"
                onClick={() => {
                    scheduleSuccess
                        ? callWfeFacade({
                              scheduleParams: props.scheduleInstallation,
                              contactDetails,
                              correlationId: correlationId ?? "",
                              subscriptionId: subscriptions?.collection
                                  .filter((itemGroup) => itemGroup.serviceGroup === "TV")[0]
                                  .id.toString(),
                          })
                        : props.onClickConfirmSchedule();
                }}
            >
                {scheduleSuccess ? t("customer:confirmInstallation") : t("customer:confirmSchedule")}
            </Button>
        </Grid>
        */}
                </Grid>
            )}
        </div>
    );
};

export default AddNewOffer;
